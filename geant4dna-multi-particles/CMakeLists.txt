cmake_minimum_required(VERSION 3.5 FATAL_ERROR)
project(geant4dna-multi-particles)

find_package(Geant4 REQUIRED)

add_executable(geant4dna-multi-particles main.cc)
target_link_libraries(geant4dna-multi-particles ${Geant4_LIBRARIES})
target_include_directories(geant4dna-multi-particles PUBLIC ${Geant4_INCLUDE_DIRS})

add_custom_command(TARGET geant4dna-multi-particles
                   POST_BUILD
                   COMMAND ${CMAKE_COMMAND} -E copy_if_different
                           "${CMAKE_SOURCE_DIR}/vis.mac"
                           "${CMAKE_BINARY_DIR}/vis.mac"
                   COMMENT "Copying vis.mac to build directory")

configure_file(
    "${CMAKE_SOURCE_DIR}/particle_energy(MeV)_amu_charge.txt"
    "${CMAKE_BINARY_DIR}/particle_energy(MeV)_amu_charge.txt"
    COPYONLY
)

add_dependencies(geant4dna-multi-particles geant4dna-multi-particles)

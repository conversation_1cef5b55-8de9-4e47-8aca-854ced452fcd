#------------------------------------------------------------------------------
# vis.mac: Visualization Macro (Diagnostic Version)
#------------------------------------------------------------------------------

# --- Initialization ---
# Ensure you are using a graphical driver from YOUR available list.
# TSGQtZB was identified from your previous logs.
/vis/open TSGQtZB 800x600-0+0
/vis/verbose errors

# --- UI Command System Diagnostics ---
/control/echo "--- Testing Basic UI Commands ---"
/control/echo "Current command directory (should be /):"
/control/pwd
/control/echo "Listing commands in current (/) directory:"
/control/ls
# Or try the alias 'lc'
# /control/lc

# Attempt to change to /vis/ directory
/control/echo "Attempting to change directory to /vis/ ..."
/control/cd /vis/
/control/echo "Current command directory after 'cd /vis/':"
/control/pwd
/control/echo "Listing commands in current (/vis/) directory:"
/control/ls
# /control/lc

# Change back to the root directory
/control/cd /
/control/echo "Changed directory back to /"
/control/pwd
/control/echo "--- End of Basic UI Command Test ---"

# --- Basic Visualization Attempt (if UI tests suggest /vis/ is somewhat functional) ---
# Set a simple background
/vis/viewer/set/background lightgrey

# Draw the geometry
# This is a key step. If this fails, no further geometry-specific vis commands will work.
/vis/drawVolume

# Refresh the viewer to show the geometry
/vis/viewer/refresh

# Basic camera settings - try only if drawVolume seems to work
/vis/viewer/set/viewpointThetaPhi 60 45
/vis/viewer/zoomToFit
/vis/viewer/zoom 1.5 # Zoom out a little after fitting
/vis/viewer/refresh

# Simplest trajectory drawing
/tracking/storeTrajectory 1
/vis/scene/add/trajectories
# Use the most basic default model for trajectories
/vis/modeling/trajectories/create/default
# Set a default color for these trajectories
/vis/modeling/trajectories/default/setDrawColor green
/vis/modeling/trajectories/default/setLineWidth 2

# Accumulate events
/vis/scene/endOfEventAction accumulate

# Final refresh
/vis/viewer/refresh

G4cout << "Diagnostic vis.mac (Attempt) has been executed." << G4endl
G4cout << "Please check the console output for results of '/control/ls' and '/control/pwd'." << G4endl
G4cout << "Also check if a visualization window appeared and shows the geometry." << G4endl
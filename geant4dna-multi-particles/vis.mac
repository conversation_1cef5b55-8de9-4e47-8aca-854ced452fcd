#------------------------------------------------------------------------------
# vis.mac: Visualization Macro for Geant4 Simulation
#
# This macro sets up common visualization settings for interactive sessions.
# Your C++ application should execute this via:
# /control/execute vis.mac
#------------------------------------------------------------------------------

# --- Initialization and Verbosity ---
# Open an OpenGL viewer. OGLIStored is good for interactive sessions.
# Alternatives: OGL, OGLSQt (if Qt is enabled and preferred)
/vis/open OGLIStored 600x600-0+0

# Set visualization verbosity level (errors, warnings, all, etc.)
/vis/verbose errors

# --- Viewer Settings ---
# Set background color (e.g., white, black, lightblue)
/vis/viewer/set/background white

# Set drawing style (surface or wireframe)
/vis/viewer/set/style surface
# /vis/viewer/set/style wireframe # Uncomment for a wireframe view

# Disable auto refresh during command processing for smoother setup
/vis/viewer/set/autoRefresh false

# --- Geometry Visualization ---
# Draw the simulation geometry
/vis/drawVolume

# Visualization attributes for logical volumes:
# World Volume: Make it visible but highly transparent to see inside.
/vis/logicalVolume/set/visibility World true
/vis/logicalVolume/set/forceWireframe World false
/vis/logicalVolume/set/colour World 0.85 0.85 0.85 0.05 # Light grey, very low opacity

# Target Volume: Make it clearly visible.
/vis/logicalVolume/set/visibility Target true
/vis/logicalVolume/set/forceWireframe Target false # Solid
/vis/logicalVolume/set/colour Target 1.0 0.0 0.0 0.6 # Red, medium opacity

# --- Trajectory Visualization ---
# Ensure trajectories are stored for visualization
# (0=off, 1=all particles, 2=charged, 3=neutral)
/tracking/storeTrajectory 1

# Add trajectories to the current scene
# 'smooth' gives nice lines, 'rich' can show more attributes like momentum arrows
/vis/scene/add/trajectories smooth
# /vis/scene/add/trajectories rich

# Option 1: Color trajectories by charge (Recommended for your multi-ion setup)
/vis/modeling/trajectories/create/drawByCharge
/vis/modeling/trajectories/drawByCharge-0/setLineWidth 2
# Default color for charges not explicitly set (e.g., for very high charges)
/vis/modeling/trajectories/drawByCharge-0/default/setDrawStepPts false # Do not draw step points by default
/vis/modeling/trajectories/drawByCharge-0/default/setLineColour white # Default line color for unassigned charges
# Specific charge colors:
/vis/modeling/trajectories/drawByCharge-0/set 0 green   # Neutral particles (e.g., gamma, neutron)
/vis/modeling/trajectories/drawByCharge-0/set +1 blue   # Charge +1 (e.g., proton)
/vis/modeling/trajectories/drawByCharge-0/set -1 red    # Charge -1 (e.g., electron)
/vis/modeling/trajectories/drawByCharge-0/set +2 yellow # Charge +2 (e.g., alpha, He-3)
/vis/modeling/trajectories/drawByCharge-0/set +3 orange
/vis/modeling/trajectories/drawByCharge-0/set +6 magenta # Example for Carbon ions (Z=6)
/vis/modeling/trajectories/drawByCharge-0/set +10 cyan  # Example for Neon ions (Z=10)
# Add more /vis/modeling/trajectories/drawByCharge-0/set [charge_value] [color_name] lines as needed

# Option 2: Color trajectories by particle type (Alternative)
# If you prefer this, comment out the "drawByCharge" section above and uncomment this:
# /vis/modeling/trajectories/create/drawByParticleID
# /vis/modeling/trajectories/drawByParticleID-0/setLineWidth 2
# /vis/modeling/trajectories/drawByParticleID-0/set e- blue
# /vis/modeling/trajectories/drawByParticleID-0/set gamma green
# /vis/modeling/trajectories/drawByParticleID-0/set proton red
# /vis/modeling/trajectories/drawByParticleID-0/set alpha yellow
# /vis/modeling/trajectories/drawByParticleID-0/set deuteron cyan
# /vis/modeling/trajectories/drawByParticleID-0/set triton magenta
# /vis/modeling/trajectories/drawByParticleID-0/set He3 orange
# /vis/modeling/trajectories/drawByParticleID-0/set GenericIon white # Default for other ions
# (Note: "GenericIon" is a common Geant4 name, but specific Z,A,Q ions might get names like "C12[6.0]" etc.)

# --- Scene Embellishments ---
# Add coordinate axes at the origin (length unit)
# World radius is 10 um, target radius is 5 um. Axes of 2-5 um would be reasonable.
/vis/scene/add/axes 0 0 0 3 um

# Add a scale bar (optional)
# /vis/scene/add/scale 2 um true # length [unit] (auto true/false for auto placement)
# /vis/scene/add/text 0 0 -3 um 10 0 0 2 um # x y z size(ignored) H V Text

# --- Camera and Lighting ---
# Set initial viewpoint (theta, phi in degrees) and zoom
/vis/viewer/set/viewpointThetaPhi 60 30
/vis/viewer/zoomToFit
/vis/viewer/zoom 1.2 # Zoom out a bit after zoomToFit for better overview

# Lighting settings
/vis/viewer/set/lightsVector 1 1 1     # Main light source direction
/vis/viewer/set/lightsIntensity 1.0   # Overall light intensity multiplier
/vis/viewer/set/ambientLight white 0.5  # Ambient light color and intensity
# /vis/viewer/set/抗锯齿 true # Correct command /vis/viewer/set/antialiasing true (if supported)

# --- Event Display and Refresh ---
# Accumulate trajectories from multiple events in the same view
/vis/scene/endOfEventAction accumulate
# Or, to clear and refresh for each event:
# /vis/scene/endOfEventAction refresh
# Or, accumulate N events:
# /vis/scene/endOfEventAction accumulate 100 # Keep last 100 event trajectories

# Enable auto refresh now that setup is done
/vis/viewer/set/autoRefresh true

# Refresh the viewer to apply all settings
/vis/viewer/refresh

# --- Optional: Initial Run ---
# You can trigger a few events to see something immediately.
# This is optional as you can also do this from the GUI or command line.
# /run/beamOn 1

# --- Useful command for interactive sessions ---
# /control/manual # Prints available UI commands (useful if you're exploring in the GUI)

G4cout << "vis.mac executed." << G4endl
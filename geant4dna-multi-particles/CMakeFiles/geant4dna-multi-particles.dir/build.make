# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.28

# Delete rule output on recipe failure.
.DELETE_ON_ERROR:

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /usr/bin/cmake

# The command to remove a file.
RM = /usr/bin/cmake -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /home/<USER>/geant4dna-multi-particles

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /home/<USER>/geant4dna-multi-particles

# Include any dependencies generated for this target.
include CMakeFiles/geant4dna-multi-particles.dir/depend.make
# Include any dependencies generated by the compiler for this target.
include CMakeFiles/geant4dna-multi-particles.dir/compiler_depend.make

# Include the progress variables for this target.
include CMakeFiles/geant4dna-multi-particles.dir/progress.make

# Include the compile flags for this target's objects.
include CMakeFiles/geant4dna-multi-particles.dir/flags.make

CMakeFiles/geant4dna-multi-particles.dir/main.cc.o: CMakeFiles/geant4dna-multi-particles.dir/flags.make
CMakeFiles/geant4dna-multi-particles.dir/main.cc.o: main.cc
CMakeFiles/geant4dna-multi-particles.dir/main.cc.o: CMakeFiles/geant4dna-multi-particles.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/geant4dna-multi-particles/CMakeFiles --progress-num=$(CMAKE_PROGRESS_1) "Building CXX object CMakeFiles/geant4dna-multi-particles.dir/main.cc.o"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT CMakeFiles/geant4dna-multi-particles.dir/main.cc.o -MF CMakeFiles/geant4dna-multi-particles.dir/main.cc.o.d -o CMakeFiles/geant4dna-multi-particles.dir/main.cc.o -c /home/<USER>/geant4dna-multi-particles/main.cc

CMakeFiles/geant4dna-multi-particles.dir/main.cc.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/geant4dna-multi-particles.dir/main.cc.i"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/geant4dna-multi-particles/main.cc > CMakeFiles/geant4dna-multi-particles.dir/main.cc.i

CMakeFiles/geant4dna-multi-particles.dir/main.cc.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/geant4dna-multi-particles.dir/main.cc.s"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/geant4dna-multi-particles/main.cc -o CMakeFiles/geant4dna-multi-particles.dir/main.cc.s

# Object files for target geant4dna-multi-particles
geant4dna__multi__particles_OBJECTS = \
"CMakeFiles/geant4dna-multi-particles.dir/main.cc.o"

# External object files for target geant4dna-multi-particles
geant4dna__multi__particles_EXTERNAL_OBJECTS =

geant4dna-multi-particles: CMakeFiles/geant4dna-multi-particles.dir/main.cc.o
geant4dna-multi-particles: CMakeFiles/geant4dna-multi-particles.dir/build.make
geant4dna-multi-particles: /cvmfs/oasis.opensciencegrid.org/jlab/geant4/ubuntu24-gcc13/geant4/11.3.1/lib/libG4Tree.so
geant4dna-multi-particles: /cvmfs/oasis.opensciencegrid.org/jlab/geant4/ubuntu24-gcc13/geant4/11.3.1/lib/libG4FR.so
geant4dna-multi-particles: /cvmfs/oasis.opensciencegrid.org/jlab/geant4/ubuntu24-gcc13/geant4/11.3.1/lib/libG4GMocren.so
geant4dna-multi-particles: /cvmfs/oasis.opensciencegrid.org/jlab/geant4/ubuntu24-gcc13/geant4/11.3.1/lib/libG4visHepRep.so
geant4dna-multi-particles: /cvmfs/oasis.opensciencegrid.org/jlab/geant4/ubuntu24-gcc13/geant4/11.3.1/lib/libG4RayTracer.so
geant4dna-multi-particles: /cvmfs/oasis.opensciencegrid.org/jlab/geant4/ubuntu24-gcc13/geant4/11.3.1/lib/libG4VRML.so
geant4dna-multi-particles: /cvmfs/oasis.opensciencegrid.org/jlab/geant4/ubuntu24-gcc13/geant4/11.3.1/lib/libG4ToolsSG.so
geant4dna-multi-particles: /cvmfs/oasis.opensciencegrid.org/jlab/geant4/ubuntu24-gcc13/geant4/11.3.1/lib/libG4vis_management.so
geant4dna-multi-particles: /cvmfs/oasis.opensciencegrid.org/jlab/geant4/ubuntu24-gcc13/geant4/11.3.1/lib/libG4modeling.so
geant4dna-multi-particles: /cvmfs/oasis.opensciencegrid.org/jlab/geant4/ubuntu24-gcc13/geant4/11.3.1/lib/libG4interfaces.so
geant4dna-multi-particles: /cvmfs/oasis.opensciencegrid.org/jlab/geant4/ubuntu24-gcc13/geant4/11.3.1/lib/libG4mctruth.so
geant4dna-multi-particles: /cvmfs/oasis.opensciencegrid.org/jlab/geant4/ubuntu24-gcc13/geant4/11.3.1/lib/libG4geomtext.so
geant4dna-multi-particles: /cvmfs/oasis.opensciencegrid.org/jlab/geant4/ubuntu24-gcc13/geant4/11.3.1/lib/libG4gdml.so
geant4dna-multi-particles: /cvmfs/oasis.opensciencegrid.org/jlab/geant4/ubuntu24-gcc13/geant4/11.3.1/lib/libG4error_propagation.so
geant4dna-multi-particles: /cvmfs/oasis.opensciencegrid.org/jlab/geant4/ubuntu24-gcc13/geant4/11.3.1/lib/libG4readout.so
geant4dna-multi-particles: /cvmfs/oasis.opensciencegrid.org/jlab/geant4/ubuntu24-gcc13/geant4/11.3.1/lib/libG4physicslists.so
geant4dna-multi-particles: /cvmfs/oasis.opensciencegrid.org/jlab/geant4/ubuntu24-gcc13/geant4/11.3.1/lib/libG4run.so
geant4dna-multi-particles: /cvmfs/oasis.opensciencegrid.org/jlab/geant4/ubuntu24-gcc13/geant4/11.3.1/lib/libG4event.so
geant4dna-multi-particles: /cvmfs/oasis.opensciencegrid.org/jlab/geant4/ubuntu24-gcc13/geant4/11.3.1/lib/libG4tracking.so
geant4dna-multi-particles: /cvmfs/oasis.opensciencegrid.org/jlab/geant4/ubuntu24-gcc13/geant4/11.3.1/lib/libG4parmodels.so
geant4dna-multi-particles: /cvmfs/oasis.opensciencegrid.org/jlab/geant4/ubuntu24-gcc13/geant4/11.3.1/lib/libG4processes.so
geant4dna-multi-particles: /cvmfs/oasis.opensciencegrid.org/jlab/geant4/ubuntu24-gcc13/geant4/11.3.1/lib/libG4digits_hits.so
geant4dna-multi-particles: /cvmfs/oasis.opensciencegrid.org/jlab/geant4/ubuntu24-gcc13/geant4/11.3.1/lib/libG4track.so
geant4dna-multi-particles: /cvmfs/oasis.opensciencegrid.org/jlab/geant4/ubuntu24-gcc13/geant4/11.3.1/lib/libG4particles.so
geant4dna-multi-particles: /cvmfs/oasis.opensciencegrid.org/jlab/geant4/ubuntu24-gcc13/geant4/11.3.1/lib/libG4geometry.so
geant4dna-multi-particles: /cvmfs/oasis.opensciencegrid.org/jlab/geant4/ubuntu24-gcc13/geant4/11.3.1/lib/libG4materials.so
geant4dna-multi-particles: /cvmfs/oasis.opensciencegrid.org/jlab/geant4/ubuntu24-gcc13/geant4/11.3.1/lib/libG4graphics_reps.so
geant4dna-multi-particles: /usr/lib/x86_64-linux-gnu/libQt6Widgets.so.6.4.2
geant4dna-multi-particles: /usr/lib/x86_64-linux-gnu/libQt6Gui.so.6.4.2
geant4dna-multi-particles: /usr/lib/x86_64-linux-gnu/libQt6Core.so.6.4.2
geant4dna-multi-particles: /usr/lib/x86_64-linux-gnu/libGL.so
geant4dna-multi-particles: /cvmfs/oasis.opensciencegrid.org/jlab/geant4/ubuntu24-gcc13/xercesc/3.2.5/lib/libxerces-c.so
geant4dna-multi-particles: /cvmfs/oasis.opensciencegrid.org/jlab/geant4/ubuntu24-gcc13/geant4/11.3.1/lib/libG4analysis.so
geant4dna-multi-particles: /usr/lib/x86_64-linux-gnu/libz.so
geant4dna-multi-particles: /usr/lib/x86_64-linux-gnu/libexpat.so
geant4dna-multi-particles: /cvmfs/oasis.opensciencegrid.org/jlab/geant4/ubuntu24-gcc13/geant4/11.3.1/lib/libG4intercoms.so
geant4dna-multi-particles: /cvmfs/oasis.opensciencegrid.org/jlab/geant4/ubuntu24-gcc13/geant4/11.3.1/lib/libG4global.so
geant4dna-multi-particles: /cvmfs/oasis.opensciencegrid.org/jlab/geant4/ubuntu24-gcc13/geant4/11.3.1/lib/libG4ptl.so.3.0.0
geant4dna-multi-particles: /cvmfs/oasis.opensciencegrid.org/jlab/geant4/ubuntu24-gcc13/clhep/*******/lib/libCLHEP-*******.so
geant4dna-multi-particles: CMakeFiles/geant4dna-multi-particles.dir/link.txt
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --bold --progress-dir=/home/<USER>/geant4dna-multi-particles/CMakeFiles --progress-num=$(CMAKE_PROGRESS_2) "Linking CXX executable geant4dna-multi-particles"
	$(CMAKE_COMMAND) -E cmake_link_script CMakeFiles/geant4dna-multi-particles.dir/link.txt --verbose=$(VERBOSE)
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --blue --bold "Copying vis.mac to build directory"
	/usr/bin/cmake -E copy_if_different /home/<USER>/geant4dna-multi-particles/vis.mac /home/<USER>/geant4dna-multi-particles/vis.mac

# Rule to build all files generated by this target.
CMakeFiles/geant4dna-multi-particles.dir/build: geant4dna-multi-particles
.PHONY : CMakeFiles/geant4dna-multi-particles.dir/build

CMakeFiles/geant4dna-multi-particles.dir/clean:
	$(CMAKE_COMMAND) -P CMakeFiles/geant4dna-multi-particles.dir/cmake_clean.cmake
.PHONY : CMakeFiles/geant4dna-multi-particles.dir/clean

CMakeFiles/geant4dna-multi-particles.dir/depend:
	cd /home/<USER>/geant4dna-multi-particles && $(CMAKE_COMMAND) -E cmake_depends "Unix Makefiles" /home/<USER>/geant4dna-multi-particles /home/<USER>/geant4dna-multi-particles /home/<USER>/geant4dna-multi-particles /home/<USER>/geant4dna-multi-particles /home/<USER>/geant4dna-multi-particles/CMakeFiles/geant4dna-multi-particles.dir/DependInfo.cmake "--color=$(COLOR)"
.PHONY : CMakeFiles/geant4dna-multi-particles.dir/depend


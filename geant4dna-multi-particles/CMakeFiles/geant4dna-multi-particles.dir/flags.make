# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.28

# compile CXX with /usr/bin/c++
CXX_DEFINES = -DG4LIB_BUILD_DLL -DG4UI_USE_QT -DG4VIS_USE_TOOLSSG_QT_GLES -DG4VIS_USE_TOOLSSG_QT_ZB -DPTL_BUILD_DLL -DQT_CORE_LIB -DQT_GUI_LIB -DQT_NO_DEBUG -DQT_WIDGETS_LIB

CXX_INCLUDES = -isystem /cvmfs/oasis.opensciencegrid.org/jlab/geant4/ubuntu24-gcc13/geant4/11.3.1/include/Geant4 -isystem /cvmfs/oasis.opensciencegrid.org/jlab/geant4/ubuntu24-gcc13/clhep/2.4.7.1/include -isystem /usr/include/x86_64-linux-gnu/qt6/QtCore -isystem /usr/include/x86_64-linux-gnu/qt6 -isystem /usr/lib/x86_64-linux-gnu/qt6/mkspecs/linux-g++ -isystem /usr/include/x86_64-linux-gnu/qt6/QtGui -isystem /usr/include/x86_64-linux-gnu/qt6/QtWidgets -isystem /cvmfs/oasis.opensciencegrid.org/jlab/geant4/ubuntu24-gcc13/xercesc/3.2.5/include

CXX_FLAGS = -fPIC


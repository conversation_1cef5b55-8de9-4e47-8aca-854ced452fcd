#!/usr/bin/env python3
"""
Geant4 DNA Multi-Particles Data Visualization Script

This script reads CSV files generated by the Geant4 DNA simulation and creates
various visualizations to analyze particle interactions with biological targets.

Author: Generated for Geant4 DNA Multi-Particles Project
"""

import pandas as pd
import matplotlib
matplotlib.use('Agg')  # Use non-interactive backend for headless environment
import matplotlib.pyplot as plt
import seaborn as sns
import numpy as np
import os
import glob
from pathlib import Path
import warnings

# Suppress warnings for cleaner output
warnings.filterwarnings('ignore')

# Set style for better-looking plots
plt.style.use('seaborn-v0_8')
sns.set_palette("husl")

class Geant4DataVisualizer:
    """Class to handle visualization of Geant4 simulation data."""

    def __init__(self, data_directory="build"):
        """
        Initialize the visualizer.

        Args:
            data_directory (str): Directory containing CSV files
        """
        self.data_dir = Path(data_directory)
        self.aggregated_file = self.data_dir / "all_runs_summary_data.csv"
        self.individual_files = list(self.data_dir.glob("*_Z*A*Q*_*.csv"))

    def load_aggregated_data(self):
        """Load the aggregated data file."""
        try:
            if self.aggregated_file.exists():
                df = pd.read_csv(self.aggregated_file)
                print(f"Loaded aggregated data: {len(df)} records")
                return df
            else:
                print(f"Aggregated file not found: {self.aggregated_file}")
                return None
        except Exception as e:
            print(f"Error loading aggregated data: {e}")
            return None

    def load_individual_files(self):
        """Load all individual CSV files."""
        all_data = []

        for file_path in self.individual_files:
            try:
                df = pd.read_csv(file_path)
                # Extract particle info from filename
                filename = file_path.stem
                parts = filename.split('_')
                if len(parts) >= 4:
                    ion = parts[0]
                    z_info = parts[1]  # Z#A#Q#
                    energy_info = parts[2]  # #p###MeV

                    # Parse Z, A, Q from filename
                    z_val = int(z_info.split('A')[0][1:])  # Remove 'Z' and get number
                    a_val = int(z_info.split('A')[1].split('Q')[0])
                    q_val = int(z_info.split('Q')[1])

                    # Parse energy
                    energy_str = energy_info.replace('p', '.').replace('MeV', '')
                    energy_val = float(energy_str)

                    # Add metadata to dataframe
                    df['IonFromFile'] = ion
                    df['Z'] = z_val
                    df['A'] = a_val
                    df['Q'] = q_val
                    df['Energy_MeV'] = energy_val
                    df['Filename'] = filename

                    all_data.append(df)

            except Exception as e:
                print(f"Error loading {file_path}: {e}")
                continue

        if all_data:
            combined_df = pd.concat(all_data, ignore_index=True)
            print(f"Loaded {len(all_data)} individual files with {len(combined_df)} total records")
            return combined_df
        else:
            print("No individual files could be loaded")
            return None

    def create_energy_deposition_plots(self, df):
        """Create plots for energy deposition analysis."""
        if df is None or df.empty:
            print("No data available for energy deposition plots")
            return

        fig, axes = plt.subplots(2, 2, figsize=(15, 12))
        fig.suptitle('Energy Deposition Analysis', fontsize=16, fontweight='bold')

        # Plot 1: Energy deposition by ion type
        if 'IonFromFile' in df.columns:
            ax1 = axes[0, 0]
            ion_edep = df.groupby('IonFromFile')['Target_Edep_keV'].agg(['mean', 'std', 'count'])
            ion_edep['mean'].plot(kind='bar', ax=ax1, color='skyblue', alpha=0.7)
            ax1.set_title('Average Energy Deposition by Ion Type')
            ax1.set_ylabel('Energy Deposition (keV)')
            ax1.set_xlabel('Ion Type')
            ax1.tick_params(axis='x', rotation=45)

            # Add error bars if we have std data
            if not ion_edep['std'].isna().all():
                ax1.errorbar(range(len(ion_edep)), ion_edep['mean'],
                           yerr=ion_edep['std'], fmt='none', color='red', capsize=5)

        # Plot 2: Energy deposition distribution
        ax2 = axes[0, 1]
        df['Target_Edep_keV'].hist(bins=20, ax=ax2, color='lightgreen', alpha=0.7, edgecolor='black')
        ax2.set_title('Energy Deposition Distribution')
        ax2.set_xlabel('Energy Deposition (keV)')
        ax2.set_ylabel('Frequency')

        # Plot 3: Energy deposition vs atomic number (if available)
        if 'Z' in df.columns:
            ax3 = axes[1, 0]
            z_edep = df.groupby('Z')['Target_Edep_keV'].mean()
            z_edep.plot(kind='bar', ax=ax3, color='orange', alpha=0.7)
            ax3.set_title('Average Energy Deposition by Atomic Number')
            ax3.set_ylabel('Energy Deposition (keV)')
            ax3.set_xlabel('Atomic Number (Z)')

        # Plot 4: Energy deposition vs charge
        if 'Q' in df.columns:
            ax4 = axes[1, 1]
            q_edep = df.groupby('Q')['Target_Edep_keV'].mean()
            q_edep.plot(kind='bar', ax=ax4, color='purple', alpha=0.7)
            ax4.set_title('Average Energy Deposition by Charge')
            ax4.set_ylabel('Energy Deposition (keV)')
            ax4.set_xlabel('Charge (Q)')

        plt.tight_layout()
        plt.savefig(self.data_dir / 'energy_deposition_analysis.png', dpi=300, bbox_inches='tight')
        plt.close()

    def create_track_length_plots(self, df):
        """Create plots for track length analysis."""
        if df is None or df.empty:
            print("No data available for track length plots")
            return

        fig, axes = plt.subplots(2, 2, figsize=(15, 12))
        fig.suptitle('Track Length Analysis', fontsize=16, fontweight='bold')

        # Plot 1: Track length by ion type
        if 'IonFromFile' in df.columns:
            ax1 = axes[0, 0]
            ion_track = df.groupby('IonFromFile')['Track_L_um'].agg(['mean', 'std'])
            ion_track['mean'].plot(kind='bar', ax=ax1, color='coral', alpha=0.7)
            ax1.set_title('Average Track Length by Ion Type')
            ax1.set_ylabel('Track Length (μm)')
            ax1.set_xlabel('Ion Type')
            ax1.tick_params(axis='x', rotation=45)

            # Add error bars
            if not ion_track['std'].isna().all():
                ax1.errorbar(range(len(ion_track)), ion_track['mean'],
                           yerr=ion_track['std'], fmt='none', color='red', capsize=5)

        # Plot 2: Track length distribution
        ax2 = axes[0, 1]
        df['Track_L_um'].hist(bins=20, ax=ax2, color='lightblue', alpha=0.7, edgecolor='black')
        ax2.set_title('Track Length Distribution')
        ax2.set_xlabel('Track Length (μm)')
        ax2.set_ylabel('Frequency')

        # Plot 3: Track length vs energy deposition
        ax3 = axes[1, 0]
        ax3.scatter(df['Track_L_um'], df['Target_Edep_keV'], alpha=0.6, color='green')
        ax3.set_title('Track Length vs Energy Deposition')
        ax3.set_xlabel('Track Length (μm)')
        ax3.set_ylabel('Energy Deposition (keV)')

        # Add correlation coefficient
        if len(df) > 1:
            corr = df['Track_L_um'].corr(df['Target_Edep_keV'])
            ax3.text(0.05, 0.95, f'Correlation: {corr:.3f}',
                    transform=ax3.transAxes, bbox=dict(boxstyle="round", facecolor='white'))

        # Plot 4: Track length vs atomic number
        if 'Z' in df.columns:
            ax4 = axes[1, 1]
            z_track = df.groupby('Z')['Track_L_um'].mean()
            z_track.plot(kind='bar', ax=ax4, color='gold', alpha=0.7)
            ax4.set_title('Average Track Length by Atomic Number')
            ax4.set_ylabel('Track Length (μm)')
            ax4.set_xlabel('Atomic Number (Z)')

        plt.tight_layout()
        plt.savefig(self.data_dir / 'track_length_analysis.png', dpi=300, bbox_inches='tight')
        plt.close()

    def create_particle_comparison_plots(self, df):
        """Create comparative plots between different particles."""
        if df is None or df.empty:
            print("No data available for particle comparison plots")
            return

        if 'IonFromFile' not in df.columns:
            print("Ion information not available for comparison plots")
            return

        fig, axes = plt.subplots(2, 2, figsize=(15, 12))
        fig.suptitle('Particle Comparison Analysis', fontsize=16, fontweight='bold')

        # Plot 1: Box plot of energy deposition by ion
        ax1 = axes[0, 0]
        df.boxplot(column='Target_Edep_keV', by='IonFromFile', ax=ax1)
        ax1.set_title('Energy Deposition Distribution by Ion Type')
        ax1.set_ylabel('Energy Deposition (keV)')
        ax1.set_xlabel('Ion Type')

        # Plot 2: Box plot of track length by ion
        ax2 = axes[0, 1]
        df.boxplot(column='Track_L_um', by='IonFromFile', ax=ax2)
        ax2.set_title('Track Length Distribution by Ion Type')
        ax2.set_ylabel('Track Length (μm)')
        ax2.set_xlabel('Ion Type')

        # Plot 3: Scatter plot colored by ion type
        ax3 = axes[1, 0]
        for ion in df['IonFromFile'].unique():
            ion_data = df[df['IonFromFile'] == ion]
            ax3.scatter(ion_data['Track_L_um'], ion_data['Target_Edep_keV'],
                       label=ion, alpha=0.7, s=50)
        ax3.set_xlabel('Track Length (μm)')
        ax3.set_ylabel('Energy Deposition (keV)')
        ax3.set_title('Track Length vs Energy Deposition by Ion Type')
        ax3.legend()

        # Plot 4: Linear Energy Transfer (LET) approximation
        ax4 = axes[1, 1]
        df['LET_keV_per_um'] = df['Target_Edep_keV'] / df['Track_L_um']
        df['LET_keV_per_um'].replace([np.inf, -np.inf], np.nan, inplace=True)
        df.dropna(subset=['LET_keV_per_um'], inplace=True)

        if not df.empty:
            let_by_ion = df.groupby('IonFromFile')['LET_keV_per_um'].mean()
            let_by_ion.plot(kind='bar', ax=ax4, color='red', alpha=0.7)
            ax4.set_title('Linear Energy Transfer (LET) by Ion Type')
            ax4.set_ylabel('LET (keV/μm)')
            ax4.set_xlabel('Ion Type')
            ax4.tick_params(axis='x', rotation=45)

        plt.tight_layout()
        plt.savefig(self.data_dir / 'particle_comparison_analysis.png', dpi=300, bbox_inches='tight')
        plt.close()

    def create_summary_statistics(self, df):
        """Create and display summary statistics."""
        if df is None or df.empty:
            print("No data available for summary statistics")
            return

        print("\n" + "="*60)
        print("GEANT4 DNA SIMULATION - SUMMARY STATISTICS")
        print("="*60)

        # Basic statistics
        print(f"\nTotal number of events: {len(df)}")

        if 'IonFromFile' in df.columns:
            print(f"Number of different ion types: {df['IonFromFile'].nunique()}")
            print(f"Ion types: {', '.join(df['IonFromFile'].unique())}")

        if 'RunID' in df.columns:
            print(f"Number of simulation runs: {df['RunID'].nunique()}")

        # Energy deposition statistics
        print(f"\nEnergy Deposition Statistics:")
        print(f"  Mean: {df['Target_Edep_keV'].mean():.3f} keV")
        print(f"  Std:  {df['Target_Edep_keV'].std():.3f} keV")
        print(f"  Min:  {df['Target_Edep_keV'].min():.3f} keV")
        print(f"  Max:  {df['Target_Edep_keV'].max():.3f} keV")

        # Track length statistics
        print(f"\nTrack Length Statistics:")
        print(f"  Mean: {df['Track_L_um'].mean():.3f} μm")
        print(f"  Std:  {df['Track_L_um'].std():.3f} μm")
        print(f"  Min:  {df['Track_L_um'].min():.3f} μm")
        print(f"  Max:  {df['Track_L_um'].max():.3f} μm")

        # Per-ion statistics
        if 'IonFromFile' in df.columns:
            print(f"\nPer-Ion Statistics:")
            ion_stats = df.groupby('IonFromFile').agg({
                'Target_Edep_keV': ['mean', 'std', 'count'],
                'Track_L_um': ['mean', 'std']
            }).round(3)
            print(ion_stats)

        # Calculate LET if possible
        if len(df) > 0:
            df_temp = df.copy()
            df_temp['LET_keV_per_um'] = df_temp['Target_Edep_keV'] / df_temp['Track_L_um']
            df_temp['LET_keV_per_um'].replace([np.inf, -np.inf], np.nan, inplace=True)
            df_temp.dropna(subset=['LET_keV_per_um'], inplace=True)

            if not df_temp.empty:
                print(f"\nLinear Energy Transfer (LET) Statistics:")
                print(f"  Mean: {df_temp['LET_keV_per_um'].mean():.3f} keV/μm")
                print(f"  Std:  {df_temp['LET_keV_per_um'].std():.3f} keV/μm")
                print(f"  Min:  {df_temp['LET_keV_per_um'].min():.3f} keV/μm")
                print(f"  Max:  {df_temp['LET_keV_per_um'].max():.3f} keV/μm")

        print("="*60)

    def run_complete_analysis(self):
        """Run the complete visualization analysis."""
        print("Starting Geant4 DNA Multi-Particles Data Analysis...")
        print(f"Data directory: {self.data_dir}")

        # Try to load aggregated data first
        df = self.load_aggregated_data()

        # If no aggregated data, try individual files
        if df is None or df.empty:
            print("Attempting to load individual CSV files...")
            df = self.load_individual_files()

        if df is None or df.empty:
            print("ERROR: No data could be loaded. Please check:")
            print("1. CSV files exist in the specified directory")
            print("2. CSV files have the correct format")
            print("3. File permissions allow reading")
            return False

        # Create all visualizations
        try:
            self.create_summary_statistics(df)
            self.create_energy_deposition_plots(df)
            self.create_track_length_plots(df)
            self.create_particle_comparison_plots(df)

            print(f"\nAnalysis complete! Plots saved in: {self.data_dir}")
            print("Generated files:")
            print("  - energy_deposition_analysis.png")
            print("  - track_length_analysis.png")
            print("  - particle_comparison_analysis.png")

            return True

        except Exception as e:
            print(f"ERROR during analysis: {e}")
            import traceback
            traceback.print_exc()
            return False


def main():
    """Main function to run the visualization."""
    # Check if we're in the right directory
    current_dir = Path.cwd()

    # Look for build directory or CSV files
    possible_dirs = [
        current_dir / "build",
        current_dir,
        current_dir / "geant4dna-multi-particles" / "build",
        current_dir / "geant4dna-multi-particles"
    ]

    data_dir = None
    for dir_path in possible_dirs:
        if dir_path.exists() and any(dir_path.glob("*.csv")):
            data_dir = dir_path
            break

    if data_dir is None:
        print("ERROR: Could not find CSV data files.")
        print("Please ensure you are running this script from the correct directory.")
        print("Expected to find CSV files in one of these locations:")
        for dir_path in possible_dirs:
            print(f"  - {dir_path}")
        return

    # Create visualizer and run analysis
    visualizer = Geant4DataVisualizer(data_dir)
    success = visualizer.run_complete_analysis()

    if success:
        print("\n✓ Visualization completed successfully!")
    else:
        print("\n✗ Visualization failed. Please check the error messages above.")


if __name__ == "__main__":
    main()

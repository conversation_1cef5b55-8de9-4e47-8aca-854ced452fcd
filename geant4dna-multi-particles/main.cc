#include "G4RunManager.hh"
#include "G4UImanager.hh"
#include "G4NistManager.hh"
#include "G4Sphere.hh"
#include "G4LogicalVolume.hh"
#include "G4PVPlacement.hh"
#include "G4SystemOfUnits.hh"
#include "G4PhysicalConstants.hh"
#include "G4ParticleGun.hh"
#include "G4ParticleTable.hh"
#include "G4IonTable.hh" // For defining ions with Z, A, Q
#include "G4ParticleDefinition.hh"
#include "G4EmDNAPhysics_option4.hh"
#include "G4VUserPrimaryGeneratorAction.hh"
#include "G4VUserDetectorConstruction.hh"
#include "G4VModularPhysicsList.hh"
#include "G4UserRunAction.hh"
#include "G4UserEventAction.hh"
#include "G4UserSteppingAction.hh"
#include "G4Step.hh"
#include "G4ios.hh"
#include "Randomize.hh"
#include "G4Run.hh"

#include "G4VisExecutive.hh"
#include "G4UIExecutive.hh"

#include <fstream>
#include <string>
#include <vector>
#include <cmath>
#include <sstream>
#include <iomanip>   // For std::fixed, std::setprecision
#include <stdexcept> // For std::invalid_argument, std::out_of_range
#include <map>       // For element symbol to Z map
#include <set>       // For unique particle-energy pairs
#include <algorithm> // For std::replace
#include <cctype>    // For std::isdigit, std::isalpha

// --- Data Structures ---
// Holds parameters for a single simulation configuration
struct SimulationRunParameters {
    std::string rawIonStrFromFile;      // e.g., "4He", "12C" from input file
    double energyMeV;                 // Energy in MeV from input file
    G4int chargeFromFile;             // Ionic charge Q from input file
    G4int amuFromFile;                // Mass number A from input file

    // Derived/determined values:
    std::string geant4ParticleIdToUse; // "proton", "alpha", or "customIon" (marker)
    G4int Z;                          // Derived atomic number

    std::string individualCsvFilename;  // Filename for this specific run's CSV

    // For std::set to ensure uniqueness of runs based on input values
    bool operator<(const SimulationRunParameters& other) const {
        if (rawIonStrFromFile != other.rawIonStrFromFile) return rawIonStrFromFile < other.rawIonStrFromFile;
        // Crude comparison for doubles, consider epsilon for more robust floating point comparison if needed
        if (std::abs(energyMeV - other.energyMeV) > 1e-9) return energyMeV < other.energyMeV; // Using tolerance
        if (chargeFromFile != other.chargeFromFile) return chargeFromFile < other.chargeFromFile;
        return amuFromFile < other.amuFromFile;
    }
};

// Holds data for one entry in the aggregated CSV file
struct AggregatedDataEntry {
    std::string rawIonStr;      // The "Ion" string from the input file
    G4int Z;                  // Atomic number
    G4int A;                  // Mass number (amu)
    G4int Q_ionic;            // Ionic charge
    G4int runID;
    G4int eventID;
    double edepKeV;
    double trackLUm;
};


// --- Global Helper Data & Functions ---
const std::map<std::string, int> elementZMap = {
    {"H", 1}, {"He", 2}, {"Li", 3}, {"B", 5}, {"C", 6}, {"N", 7}, {"O", 8}, {"F", 9},
    {"Ne", 10}, {"Na", 11}, {"Mg", 12}, {"Al", 13}, {"Si", 14}, {"P", 15}, {"S", 16},
    {"Cl", 17}, {"Ar", 18}, {"K", 19}, {"Ca", 20}, {"Sc", 21}, {"Ti", 22}, {"V", 23},
    {"Cr", 24}, {"Mn", 25}, {"Fe", 26}, {"Co", 27}, {"Ni", 28}, {"Cu", 29}, {"Zn", 30},
    {"Ga", 31}, {"Ge", 32}, {"As", 33}, {"Se", 34}, {"Br", 35}, {"Kr", 36}, {"Rb", 37},
    {"Sr", 38}, {"Y", 39}, {"Zr", 40}, {"Nb", 41}, {"Mo", 42}, {"Tc", 43}, {"Ru", 44},
    {"Rh", 45}, {"Pd", 46}, {"Ag", 47}, {"Cd", 48}, {"In", 49}, {"Sn", 50}, {"Sb", 51},
    {"Te", 52}, {"I", 53}, {"Xe", 54}, {"Cs", 55}, {"Ba", 56}, {"La", 57}, {"Ce", 58},
    {"Pr", 59}, {"Nd", 60}, {"Pm", 61}, {"Sm", 62}, {"Eu", 63}, {"Gd", 64}, {"Tb", 65},
    {"Dy", 66}, {"Ho", 67}, {"Er", 68}, {"Tm", 69}, {"Yb", 70}, {"Lu", 71}, {"Hf", 72},
    {"Ta", 73}, {"W", 74}, {"Re", 75}, {"Os", 76}, {"Ir", 77}, {"Pt", 78}, {"Au", 79},
    {"Hg", 80}, {"Tl", 81}, {"Pb", 82}, {"Bi", 83}, {"Po", 84}, {"At", 85}, {"Rn", 86},
    {"Fr", 87}, {"Ra", 88}, {"Ac", 89}, {"Th", 90}, {"Pa", 91}, {"U", 92}
    // Add more if needed
};

std::string extractElementSymbol(const std::string& ionFileStr) {
    std::string symbol;
    for (char ch : ionFileStr) {
        if (std::isalpha(ch)) {
            symbol += ch;
        }
    }
    return symbol;
}

std::string generateSanitizedFilename(const std::string& rawIon, G4int Z, G4int A, G4int Q, double energyMeV) {
    std::ostringstream oss;
    // Use rawIon for more direct relation to input, or construct from Z,A,Q
    oss << rawIon << "_Z" << Z << "A" << A << "Q" << Q << "_"
        << std::fixed << std::setprecision(3) << energyMeV << "MeV";
    std::string baseName = oss.str();
    std::replace(baseName.begin(), baseName.end(), '.', 'p');
    std::replace(baseName.begin(), baseName.end(), '+', 'p'); // if Q can be string like "+1"
    std::replace(baseName.begin(), baseName.end(), '-', 'm'); // if Q can be string like "-1"
    // Remove any other problematic characters if necessary
    baseName.erase(std::remove_if(baseName.begin(), baseName.end(), 
        [](char c) { return !(isalnum(c) || c == '_' || c == 'p' || c == 'm'); }), baseName.end());
    return baseName + ".csv";
}

// --- Geant4 User Action Classes ---
// MyDetectorConstruction (no changes needed from previous version)
class MyDetectorConstruction : public G4VUserDetectorConstruction {
public:
    MyDetectorConstruction() : fTargetLogical(nullptr) {}
    G4VPhysicalVolume* Construct() override {
        G4NistManager* nist = G4NistManager::Instance();
        G4Material* water = nist->FindOrBuildMaterial("G4_WATER");
        G4double worldRadius = 10.0 * um;
        G4Sphere* solidWorld = new G4Sphere("World", 0., worldRadius, 0., twopi, 0., pi);
        G4LogicalVolume* logicWorld = new G4LogicalVolume(solidWorld, water, "World");
        G4double targetRadius = 5.0 * um;
        G4Sphere* solidTarget = new G4Sphere("Target", 0., targetRadius, 0., twopi, 0., pi);
        fTargetLogical = new G4LogicalVolume(solidTarget, water, "Target");
        new G4PVPlacement(nullptr, G4ThreeVector(), fTargetLogical, "Target", logicWorld, false, 0, true);
        G4VPhysicalVolume* physWorld = new G4PVPlacement(nullptr, G4ThreeVector(), logicWorld, "World", nullptr, false, 0, true);
        return physWorld;
    }
    G4LogicalVolume* GetTargetLogical() const { return fTargetLogical; }
private:
    G4LogicalVolume* fTargetLogical;
};

// MyPhysicsList (no changes needed from previous version)
class MyPhysicsList : public G4VModularPhysicsList {
public:
    MyPhysicsList() {
        RegisterPhysics(new G4EmDNAPhysics_option4());
    }
    ~MyPhysicsList() override {}
};

// MyPrimaryGenerator - Modified to use SimulationRunParameters
class MyPrimaryGenerator : public G4VUserPrimaryGeneratorAction {
public:
    MyPrimaryGenerator(const SimulationRunParameters& params, G4LogicalVolume* targetLogical)
        : fParticleGun(new G4ParticleGun(1)), 
          fSimParams(params), // Store parameters
          fTargetLogical(targetLogical) {
              
        G4ParticleTable* particleTable = G4ParticleTable::GetParticleTable();
        G4IonTable* ionTable = G4IonTable::GetIonTable();
        fParticle = nullptr;

        const std::string& particleIdMethod = fSimParams.geant4ParticleIdToUse;

        if (particleIdMethod == "proton" || particleIdMethod == "alpha" ||
            particleIdMethod == "deuteron" || particleIdMethod == "triton" ||
            particleIdMethod == "He3" || particleIdMethod == "gamma" || particleIdMethod == "e-" || particleIdMethod == "neutron") {
            fParticle = particleTable->FindParticle(particleIdMethod);
            if (fParticle) {
                // Verify AMU and Charge if possible (optional)
                // G4int def_A = G4lrint(fParticle->GetPDGMass() / amu_c2); // More complex to get A this way
                // G4int def_Q = G4lrint(fParticle->GetPDGCharge() / eplus);
                // if (def_A != fSimParams.amuFromFile || def_Q != fSimParams.chargeFromFile) {
                //     G4cout << "Warning: Predefined particle " << particleIdMethod 
                //            << " has A/Q differing from file for " << fSimParams.rawIonStrFromFile << G4endl;
                // }
            }
        } else if (particleIdMethod == "customIon") {
            // Use Z, A (amuFromFile), Q (chargeFromFile) directly
            fParticle = ionTable->GetIon(fSimParams.Z, fSimParams.amuFromFile, fSimParams.chargeFromFile);
        } else {
             G4cerr << "Unknown particleIdMethod: " << particleIdMethod << " for " << fSimParams.rawIonStrFromFile << G4endl;
        }


        if (!fParticle) { // Fallback: try creating ion directly if Z,A,Q are valid, useful if marker was missed
            if (fSimParams.Z > 0 && fSimParams.amuFromFile >= fSimParams.Z) {
                 G4cout << "Fallback: Defining ion for " << fSimParams.rawIonStrFromFile 
                        << " with Z=" << fSimParams.Z << ", A=" << fSimParams.amuFromFile << ", Q=" << fSimParams.chargeFromFile << G4endl;
                fParticle = ionTable->GetIon(fSimParams.Z, fSimParams.amuFromFile, fSimParams.chargeFromFile);
            }
        }
        
        if (!fParticle) { // Last resort: try raw name from file
             G4cout << "Last Resort Fallback: Trying to find particle by raw name: " << fSimParams.rawIonStrFromFile << G4endl;
             fParticle = particleTable->FindParticle(fSimParams.rawIonStrFromFile);
        }


        if (!fParticle) {
            G4cerr << "FATAL Error: Particle '" << fSimParams.rawIonStrFromFile 
                   << "' (parsed as method '" << particleIdMethod << "', Z=" << fSimParams.Z 
                   << ", A=" << fSimParams.amuFromFile << ", Q=" << fSimParams.chargeFromFile 
                   << ") could not be defined." << G4endl;
            G4Exception("MyPrimaryGenerator::MyPrimaryGenerator",
                        "ParticleDefinitionError", FatalException,
                        "Could not define particle.");
        }
        
        // Energy from parameters is already in MeV
        fEnergy = fSimParams.energyMeV * MeV; // Ensure it's explicitly using Geant4 unit
        fSourcePosition = G4ThreeVector(-4.999 * um, -4.999 * um, 0.0);
    }

    ~MyPrimaryGenerator() override {
        delete fParticleGun;
    }

    void GeneratePrimaries(G4Event* event) override {
        if (!fParticle) {
             G4Exception("MyPrimaryGenerator::GeneratePrimaries", "NullParticle", FatalException, "fParticle is null.");
            return;
        }
        fParticleGun->SetParticleDefinition(fParticle);
        fParticleGun->SetParticleEnergy(fEnergy); // fEnergy is already set in constructor with units
        fParticleGun->SetParticlePosition(fSourcePosition);
        G4ThreeVector dir = GenerateDirectionThroughTarget();
        fParticleGun->SetParticleMomentumDirection(dir);
        fParticleGun->GeneratePrimaryVertex(event);
    }

private:
    G4ParticleGun* fParticleGun;
    G4ParticleDefinition* fParticle;
    SimulationRunParameters fSimParams; // Store the parameters for this run
    double fEnergy; // Stored with units
    G4ThreeVector fSourcePosition;
    G4LogicalVolume* fTargetLogical;

    // GenerateDirectionThroughTarget() Method (no changes needed from previous version)
    G4ThreeVector GenerateDirectionThroughTarget() {
        G4ThreeVector targetCenter(0., 0., 0.);
        G4double radius = 5.0 * um;
        G4ThreeVector toCenter = targetCenter - fSourcePosition;
        G4double distToCenter = toCenter.mag();
        if (distToCenter <= radius + 1e-9*um) { // Add tolerance if source can be on boundary
             // If source is inside or too close, shoot towards center.
             // Or handle as an error, depending on desired behavior.
            if (distToCenter < 1e-9*um) return G4ThreeVector(0,0,1); // Source at center, shoot along Z
            return toCenter.unit(); 
        }
        G4double sinThetaMax = radius / distToCenter;
        // Clamp sinThetaMax due to potential floating point inaccuracies if distToCenter is very close to radius
        if (sinThetaMax > 1.0) sinThetaMax = 1.0;
        if (sinThetaMax < -1.0) sinThetaMax = -1.0; // Should not happen with positive radius/dist

        G4double cosThetaMax = std::sqrt(std::max(0.0, 1.0 - sinThetaMax * sinThetaMax)); // max(0.0, ..) for safety
        G4double cosTheta = (1.0 - cosThetaMax) * G4UniformRand() + cosThetaMax; // Sample uniformly solid angle of cone cap
        G4double sinTheta = std::sqrt(std::max(0.0, 1.0 - cosTheta * cosTheta)); 
        G4double phi = twopi * G4UniformRand();
        G4ThreeVector dirLocal(sinTheta * std::cos(phi), sinTheta * std::sin(phi), cosTheta);
        G4ThreeVector zPrime = toCenter.unit();
        G4ThreeVector yPrime = zPrime.orthogonal().unit();
        G4ThreeVector xPrime = yPrime.cross(zPrime);
        G4RotationMatrix rot;
        rot.setRows(xPrime, yPrime, zPrime); // Sets columns of inverse, so transpose for actual rotation from local to global
        return rot.inverse() * dirLocal; // Transform from local (cone aligned with z') to global
    }
};


// MyRunAction - Modified to handle aggregated data
class MyRunAction : public G4UserRunAction {
public:
    MyRunAction(const SimulationRunParameters& params, std::vector<AggregatedDataEntry>& global_data_collector)
        : fCurrentRunParams(params), fGlobalDataRef(global_data_collector), fRunID(0) {
        fOut.open(fCurrentRunParams.individualCsvFilename);
        if (fOut.is_open()) {
            fOut << "RunID,EventID,Target_Edep_keV,Track_L_um\n"; // Header for individual CSV
        } else {
            G4cerr << "ERROR: Could not open output file: " << fCurrentRunParams.individualCsvFilename << G4endl;
        }
    }

    ~MyRunAction() override {
        if (fOut.is_open()) fOut.close();
    }

    void BeginOfRunAction(const G4Run* run) override {
        fRunID = run->GetRunID();
        // G4Random::setTheSeed(fRunID); // Optionally seed random number generator per run
    }

    void EndOfRunAction(const G4Run*) override {
        // Any run-level summary for individual file could go here
    }
    
    G4int GetRunID() const { return fRunID;} // Needed by EventAction for aggregated data

    // This method will be called by MyEventAction to record data
    void RecordEventData(G4int eventID, G4double edep, G4double trackLength) {
        // 1. Write to individual CSV file
        if (fOut.is_open()) {
            fOut << fRunID << "," << eventID << ","
                 << edep / keV << "," << trackLength / um << "\n";
            fOut.flush();
        }

        // 2. Add to global aggregated data vector
        AggregatedDataEntry entry = {
            fCurrentRunParams.rawIonStrFromFile,
            fCurrentRunParams.Z,
            fCurrentRunParams.amuFromFile,
            fCurrentRunParams.chargeFromFile,
            fRunID,
            eventID,
            edep / keV,      // Energy deposition in keV
            trackLength / um // Track length in micrometers
        };
        fGlobalDataRef.push_back(entry);
    }

private:
    std::ofstream fOut;
    SimulationRunParameters fCurrentRunParams;
    std::vector<AggregatedDataEntry>& fGlobalDataRef; // Reference to the master list in main
    G4int fRunID;
};

// MyEventAction - Calls MyRunAction to record data
class MyEventAction : public G4UserEventAction {
public:
    MyEventAction(MyRunAction* runAction) 
        : fRunAction(runAction), fEdep(0.), fTrackLength(0.) {}

    void BeginOfEventAction(const G4Event*) override {
        fEdep = 0.;
        fTrackLength = 0.;
    }

    void EndOfEventAction(const G4Event* event) override {
        if (fRunAction) {
            fRunAction->RecordEventData(event->GetEventID(), fEdep, fTrackLength);
        }
    }

    void AddEdep(double edep) { fEdep += edep; }
    void AddTrackLength(double length) { fTrackLength += length; }

private:
    MyRunAction* fRunAction; // Pointer to the current run action
    double fEdep;
    double fTrackLength;
};

// MySteppingAction (no changes needed from previous version)
class MySteppingAction : public G4UserSteppingAction {
public:
    MySteppingAction(MyEventAction* eventAction, G4LogicalVolume* targetLogical)
        : fEventAction(eventAction), fTargetLogical(targetLogical) {}
    void UserSteppingAction(const G4Step* step) override {
        if (!fTargetLogical || !fEventAction) return;
        G4LogicalVolume* currentVolume = step->GetPreStepPoint()->GetTouchableHandle()->GetVolume()->GetLogicalVolume();
        if (currentVolume == fTargetLogical) {
            fEventAction->AddEdep(step->GetTotalEnergyDeposit());
            fEventAction->AddTrackLength(step->GetStepLength());
        }
    }
private:
    MyEventAction* fEventAction; G4LogicalVolume* fTargetLogical;
};


// --- Main Application ---
int main(int argc, char** argv) {
    G4UIExecutive* ui = nullptr;
    if (argc == 1) {
        ui = new G4UIExecutive(argc, argv);
    }

    std::vector<SimulationRunParameters>
        simulationRunsToPerform;
    std::vector<AggregatedDataEntry>
        allRunsAggregatedData; // Master list for the aggregated CSV

    // --- Read and Parse Input Particle Data File ---
    std::string inputDataFilename = "particle_energy(MeV)_amu_charge.txt"; // Source [1]
    std::ifstream inputFile(inputDataFilename);

    if (!inputFile.is_open()) {
        G4cerr << "ERROR: Could not open particle data file: " << inputDataFilename << G4endl;
        G4cerr << "Please ensure the file '" << inputDataFilename << "' exists in the execution directory." << G4endl;
        return 1;
    }

    G4cout << "Reading particle list from: " << inputDataFilename << G4endl;
    std::string line;
    std::getline(inputFile, line); // Skip header line [cite: 1]
    G4cout << "Skipped header: " << line << G4endl;

    std::set<SimulationRunParameters> uniqueRuns; // To store unique run configurations

    int lineNum = 1;
    while (std::getline(inputFile, line)) {
        lineNum++;
        if (line.empty() || line.find_first_not_of(" \t\n\v\f\r") == std::string::npos) {
            continue; // Skip empty or whitespace-only lines
        }
        std::stringstream lineStream(line);
        std::string rawIonFromFile;
        double energyFromFile;
        G4int chargeFromFile;
        G4int amuFromFile;

        lineStream >> rawIonFromFile >> energyFromFile >> chargeFromFile >> amuFromFile;

        if (lineStream.fail()) {
            G4cout << "Warning: Malformed line #" << lineNum << ": '" << line << "'. Skipping." << G4endl;
            continue;
        }
         if (rawIonFromFile.empty()){
            G4cout << "Warning: Skipping line #" << lineNum << " with empty ion string: '" << line << "'" << G4endl;
            continue;
        }


        std::string elementSym = extractElementSymbol(rawIonFromFile);
        G4int Z_derived = 0;
        if (!elementSym.empty()) {
            auto it = elementZMap.find(elementSym);
            if (it != elementZMap.end()) {
                Z_derived = it->second;
            } else {
                G4cout << "Warning: Unknown element symbol '" << elementSym << "' from ion '" << rawIonFromFile 
                       << "' on line #" << lineNum << ". Skipping." << G4endl;
                continue;
            }
        } else {
            G4cout << "Warning: Could not extract element symbol from ion '" << rawIonFromFile 
                   << "' on line #" << lineNum << ". Skipping." << G4endl;
            continue;
        }
        
        if (Z_derived <= 0 || amuFromFile < Z_derived) {
             G4cout << "Warning: Invalid Z (" << Z_derived << ") or A (" << amuFromFile 
                    << ") from ion '" << rawIonFromFile << "' on line #" << lineNum << ". Skipping." << G4endl;
            continue;
        }


        SimulationRunParameters currentParams;
        currentParams.rawIonStrFromFile = rawIonFromFile;
        currentParams.energyMeV = energyFromFile; // Energy is in MeV from file [cite: 1]
        currentParams.chargeFromFile = chargeFromFile; // Ionic charge Q [cite: 1]
        currentParams.amuFromFile = amuFromFile; // Mass number A [cite: 1]
        currentParams.Z = Z_derived;

        // Determine the method for MyPrimaryGenerator
        if (elementSym == "H" && amuFromFile == 1 && chargeFromFile == 1) currentParams.geant4ParticleIdToUse = "proton";
        else if (elementSym == "H" && amuFromFile == 2 && chargeFromFile == 1) currentParams.geant4ParticleIdToUse = "deuteron";
        else if (elementSym == "H" && amuFromFile == 3 && chargeFromFile == 1) currentParams.geant4ParticleIdToUse = "triton";
        else if (elementSym == "He" && amuFromFile == 3 && chargeFromFile == 2) currentParams.geant4ParticleIdToUse = "He3";
        else if (elementSym == "He" && amuFromFile == 4 && chargeFromFile == 2) currentParams.geant4ParticleIdToUse = "alpha";
        else currentParams.geant4ParticleIdToUse = "customIon"; // Marker for generic Z,A,Q definition

        currentParams.individualCsvFilename = generateSanitizedFilename(
            rawIonFromFile, Z_derived, amuFromFile, chargeFromFile, energyFromFile);
        
        uniqueRuns.insert(currentParams);
    }
    inputFile.close();

    // Populate the final vector of runs to perform
    for (const auto& params : uniqueRuns) {
        simulationRunsToPerform.push_back(params);
    }
    
    if (simulationRunsToPerform.empty()){
         G4cerr << "No valid unique particle-energy-charge-amu configurations found in " << inputDataFilename << ". Exiting." << G4endl;
         return 1;
    }
    G4cout << "Loaded " << simulationRunsToPerform.size() << " unique simulation configurations." << G4endl;


    // --- Geant4 Setup and Run ---
    G4RunManager* runManager = new G4RunManager();
    MyDetectorConstruction* det = new MyDetectorConstruction();
    runManager->SetUserInitialization(det);
    G4VModularPhysicsList* physicsList = new MyPhysicsList();
    runManager->SetUserInitialization(physicsList);
    
    G4VisManager* visManager = nullptr;
    if (ui) { 
        visManager = new G4VisExecutive("Quiet");
        visManager->Initialize();
    }
    G4UImanager* UImanager = G4UImanager::GetUIpointer();
    runManager->Initialize();

    // --- Loop Over Simulation Runs ---
    G4int nEventsPerRun = 2; // Default, make configurable if needed
    if (argc > 2 && std::string(argv[1]) == "-nEvents") { // Example: myapp -nEvents 1000
        try {
            nEventsPerRun = std::stoi(argv[2]);
        } catch (const std::exception& e) {
            G4cerr << "Warning: Could not parse nEvents argument. Using default: " << nEventsPerRun << G4endl;
        }
    }


    for (const auto& params : simulationRunsToPerform) {
        G4cout << "\n----------------------------------------------------------------------" << G4endl;
        G4cout << "Run Config: Ion=" << params.rawIonStrFromFile << " (Z=" << params.Z << ", A=" << params.amuFromFile << ", Q=" << params.chargeFromFile << ")"
               << ", E=" << params.energyMeV << " MeV"
               << ", File: " << params.individualCsvFilename << G4endl;
        G4cout << "----------------------------------------------------------------------" << G4endl;

        // Pass the full params struct and the reference to the aggregated data collector
        MyRunAction* runAction = new MyRunAction(params, allRunsAggregatedData);
        runManager->SetUserAction(runAction);

        MyEventAction* eventAction = new MyEventAction(runAction); // EventAction gets RunAction
        runManager->SetUserAction(eventAction);

        MySteppingAction* steppingAction = new MySteppingAction(eventAction, det->GetTargetLogical());
        runManager->SetUserAction(steppingAction);
        
        MyPrimaryGenerator* primaryGen = new MyPrimaryGenerator(params, det->GetTargetLogical());
        runManager->SetUserAction(primaryGen);

        runManager->BeamOn(nEventsPerRun);
    }

    // --- Write Aggregated Data ---
    std::string aggregatedFilename = "all_runs_summary_data.csv";
    std::ofstream aggOutFile(aggregatedFilename);
    if (aggOutFile.is_open()) {
        G4cout << "\nWriting aggregated data to: " << aggregatedFilename << G4endl;
        // Header for aggregated CSV file
        aggOutFile << "IonFromFile,Z,A,Q,RunID,EventID,Target_Edep_keV,Track_L_um\n";
        for (const auto& entry : allRunsAggregatedData) {
            aggOutFile << entry.rawIonStr << "," << entry.Z << "," << entry.A << "," << entry.Q_ionic << ","
                       << entry.runID << "," << entry.eventID << "," 
                       << std::fixed << std::setprecision(6) << entry.edepKeV << "," 
                       << std::fixed << std::setprecision(6) << entry.trackLUm << "\n";
        }
        aggOutFile.close();
        G4cout << "Aggregated data writing complete." << G4endl;
    } else {
        G4cerr << "ERROR: Could not open aggregated output file: " << aggregatedFilename << G4endl;
    }

    // --- Cleanup ---
    if (ui) {
        if (argc == 1 && UImanager) UImanager->ApplyCommand("/control/execute vis.mac");
        ui->SessionStart();
        delete ui;
    }
    if (visManager) delete visManager;
    delete runManager;

    G4cout << "\nAll simulations finished." << G4endl;
    return 0;
}